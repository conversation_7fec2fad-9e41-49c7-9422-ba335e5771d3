# Python Coding Preparation for Applied AI Engineer Role

## 🐍 Python Fundamentals for TypeScript Engineers

### Type Systems: Dynamic vs Static

**TypeScript (Static Typing)**
```typescript
interface User {
  id: number;
  name: string;
  email: string;
}

function processUser(user: User): string {
  return `Processing ${user.name} (${user.email})`;
}
```

**Python (Dynamic Typing with Type Hints)**
```python
from typing import TypedDict
from dataclasses import dataclass

# Option 1: TypedDict (similar to TS interface)
class User(TypedDict):
    id: int
    name: str
    email: str

# Option 2: Dataclass (more Pythonic)
@dataclass
class UserData:
    id: int
    name: str
    email: str

def process_user(user: User) -> str:
    return f"Processing {user['name']} ({user['email']})"

def process_user_data(user: UserData) -> str:
    return f"Processing {user.name} ({user.email})"
```

### Collections and Data Processing

**TypeScript**
```typescript
const users = [
  { id: 1, name: "<PERSON>", role: "admin" },
  { id: 2, name: "<PERSON>", role: "user" }
];

const adminUsers = users
  .filter(user => user.role === "admin")
  .map(user => user.name);
```

**Python**
```python
users = [
    {"id": 1, "name": "Alice", "role": "admin"},
    {"id": 2, "name": "Bob", "role": "user"}
]

# List comprehension (Pythonic)
admin_users = [user["name"] for user in users if user["role"] == "admin"]

# Functional approach (similar to TS)
admin_users = list(map(
    lambda user: user["name"],
    filter(lambda user: user["role"] == "admin", users)
))
```

### Async Programming Patterns

**TypeScript**
```typescript
async function fetchUserData(userId: string): Promise<User> {
  const response = await fetch(`/api/users/${userId}`);
  return response.json();
}

async function processMultipleUsers(userIds: string[]): Promise<User[]> {
  const promises = userIds.map(id => fetchUserData(id));
  return Promise.all(promises);
}
```

**Python**
```python
import asyncio
import aiohttp
from typing import List

async def fetch_user_data(session: aiohttp.ClientSession, user_id: str) -> dict:
    async with session.get(f"/api/users/{user_id}") as response:
        return await response.json()

async def process_multiple_users(user_ids: List[str]) -> List[dict]:
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_user_data(session, user_id) for user_id in user_ids]
        return await asyncio.gather(*tasks)

# Usage
async def main():
    users = await process_multiple_users(["1", "2", "3"])
    print(users)

# Run the async function
asyncio.run(main())
```

## 🔗 LangChain Framework Deep Dive

### Basic Chain Creation

```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

# Initialize LLM
llm = OpenAI(temperature=0.7)

# Create prompt template
prompt = PromptTemplate(
    input_variables=["code", "language"],
    template="""
    Review the following {language} code and provide feedback:
    
    Code:
    {code}
    
    Please provide:
    1. Code quality assessment
    2. Potential improvements
    3. Security considerations
    """
)

# Create chain
code_review_chain = LLMChain(llm=llm, prompt=prompt)

# Usage
result = code_review_chain.run(
    code="function add(a, b) { return a + b; }",
    language="JavaScript"
)
print(result)
```

### Custom Tool Creation

```python
from langchain.tools import BaseTool
from typing import Optional
import requests

class CodeAnalysisTool(BaseTool):
    name = "code_analysis"
    description = "Analyzes code for complexity and quality metrics"
    
    def _run(self, code: str) -> str:
        # Simulate code analysis
        lines = len(code.split('\n'))
        complexity = min(lines // 10, 10)  # Simple complexity metric
        
        return f"""
        Code Analysis Results:
        - Lines of code: {lines}
        - Estimated complexity: {complexity}/10
        - Recommendations: {"Refactor for better readability" if complexity > 5 else "Code looks clean"}
        """
    
    async def _arun(self, code: str) -> str:
        # Async version
        return self._run(code)

# Usage with agent
from langchain.agents import initialize_agent, AgentType

tools = [CodeAnalysisTool()]
agent = initialize_agent(
    tools, 
    llm, 
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True
)

result = agent.run("Analyze this code: def factorial(n): return 1 if n <= 1 else n * factorial(n-1)")
```

### Memory Implementation

```python
from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory
from langchain.chains import ConversationChain

# Buffer Memory (stores exact conversation)
buffer_memory = ConversationBufferMemory()

conversation = ConversationChain(
    llm=llm,
    memory=buffer_memory,
    verbose=True
)

# First interaction
response1 = conversation.predict(input="I'm working on a Python project")
print(response1)

# Second interaction (remembers context)
response2 = conversation.predict(input="What are some best practices for this language?")
print(response2)

# Summary Memory (for longer conversations)
summary_memory = ConversationSummaryMemory(llm=llm)

conversation_with_summary = ConversationChain(
    llm=llm,
    memory=summary_memory,
    verbose=True
)
```

### RAG Implementation

```python
from langchain.document_loaders import TextLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.chains import RetrievalQA

# Load and split documents
loader = TextLoader("documentation.txt")
documents = loader.load()

text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
texts = text_splitter.split_documents(documents)

# Create embeddings and vector store
embeddings = OpenAIEmbeddings()
vectorstore = Chroma.from_documents(texts, embeddings)

# Create retrieval chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    chain_type="stuff",
    retriever=vectorstore.as_retriever()
)

# Query the documents
query = "How do I implement error handling in Python?"
result = qa_chain.run(query)
print(result)
```

## 🏗 Production LLM Patterns

### Error Handling and Retry Logic

```python
import asyncio
from typing import Optional, Callable, Any
import logging
from functools import wraps

class LLMError(Exception):
    """Custom exception for LLM-related errors"""
    pass

def retry_with_backoff(max_retries: int = 3, backoff_factor: float = 2.0):
    """Decorator for retry logic with exponential backoff"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise LLMError(f"Failed after {max_retries} attempts: {str(e)}")
                    
                    wait_time = backoff_factor ** attempt
                    logging.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time}s: {str(e)}")
                    await asyncio.sleep(wait_time)
            
        return wrapper
    return decorator

@retry_with_backoff(max_retries=3)
async def call_llm_with_retry(prompt: str) -> str:
    # Simulate LLM call that might fail
    response = await llm.agenerate([prompt])
    return response.generations[0][0].text
```

### Cost Monitoring and Token Management

```python
from dataclasses import dataclass
from typing import Dict, List
import tiktoken

@dataclass
class TokenUsage:
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    estimated_cost: float

class CostTracker:
    def __init__(self):
        self.usage_history: List[TokenUsage] = []
        self.model_costs = {
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},  # per 1K tokens
            "gpt-4": {"input": 0.03, "output": 0.06}
        }
    
    def count_tokens(self, text: str, model: str = "gpt-3.5-turbo") -> int:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))
    
    def estimate_cost(self, prompt: str, completion: str, model: str = "gpt-3.5-turbo") -> TokenUsage:
        prompt_tokens = self.count_tokens(prompt, model)
        completion_tokens = self.count_tokens(completion, model)
        total_tokens = prompt_tokens + completion_tokens
        
        costs = self.model_costs[model]
        estimated_cost = (
            (prompt_tokens / 1000) * costs["input"] +
            (completion_tokens / 1000) * costs["output"]
        )
        
        usage = TokenUsage(prompt_tokens, completion_tokens, total_tokens, estimated_cost)
        self.usage_history.append(usage)
        return usage
    
    def get_total_cost(self) -> float:
        return sum(usage.estimated_cost for usage in self.usage_history)

# Usage
tracker = CostTracker()
usage = tracker.estimate_cost("What is Python?", "Python is a programming language...")
print(f"Cost: ${usage.estimated_cost:.4f}")
```

### Streaming Response Handler

```python
import asyncio
from typing import AsyncGenerator, Callable

class StreamingLLMHandler:
    def __init__(self, llm):
        self.llm = llm
        self.callbacks: List[Callable[[str], None]] = []
    
    def add_callback(self, callback: Callable[[str], None]):
        """Add callback for processing streaming tokens"""
        self.callbacks.append(callback)
    
    async def stream_response(self, prompt: str) -> AsyncGenerator[str, None]:
        """Stream LLM response token by token"""
        # This is a simplified example - actual implementation depends on LLM provider
        response = await self.llm.agenerate([prompt])
        full_text = response.generations[0][0].text
        
        # Simulate streaming by yielding words
        words = full_text.split()
        for word in words:
            # Call all registered callbacks
            for callback in self.callbacks:
                callback(word)
            
            yield word + " "
            await asyncio.sleep(0.1)  # Simulate streaming delay

# Usage
async def print_token(token: str):
    print(token, end="", flush=True)

handler = StreamingLLMHandler(llm)
handler.add_callback(print_token)

async def main():
    async for token in handler.stream_response("Explain machine learning"):
        pass  # Tokens are printed by callback

asyncio.run(main())
```

## 🧪 Practice Exercises

### Exercise 1: Data Processing Pipeline

```python
from typing import List, Dict, Any
import json
import asyncio

class DataProcessor:
    """Process JSON data with validation and transformation"""
    
    def __init__(self):
        self.processed_count = 0
    
    def validate_record(self, record: Dict[str, Any]) -> bool:
        """Validate required fields"""
        required_fields = ["id", "name", "email"]
        return all(field in record for field in required_fields)
    
    def transform_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Transform record format"""
        return {
            "user_id": record["id"],
            "full_name": record["name"].title(),
            "email_address": record["email"].lower(),
            "processed_at": "2024-01-01T00:00:00Z"
        }
    
    async def process_batch(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of records asynchronously"""
        valid_records = [r for r in records if self.validate_record(r)]
        
        # Simulate async processing
        await asyncio.sleep(0.1)
        
        transformed = [self.transform_record(r) for r in valid_records]
        self.processed_count += len(transformed)
        
        return transformed

# Test the processor
async def test_processor():
    processor = DataProcessor()
    
    test_data = [
        {"id": 1, "name": "alice smith", "email": "<EMAIL>"},
        {"id": 2, "name": "bob jones"},  # Missing email - should be filtered
        {"id": 3, "name": "charlie brown", "email": "<EMAIL>"}
    ]
    
    result = await processor.process_batch(test_data)
    print(f"Processed {len(result)} records:")
    for record in result:
        print(json.dumps(record, indent=2))

asyncio.run(test_processor())

### Exercise 2: LangChain Code Review Agent

```python
from langchain.agents import Tool, AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.llms import OpenAI
import ast
import re

class CodeReviewAgent:
    def __init__(self):
        self.llm = OpenAI(temperature=0.1)
        self.tools = self._create_tools()
        self.agent = self._create_agent()

    def _create_tools(self) -> List[Tool]:
        """Create tools for code analysis"""

        def analyze_complexity(code: str) -> str:
            """Analyze code complexity"""
            try:
                tree = ast.parse(code)
                functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
                classes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]

                return f"""
                Complexity Analysis:
                - Functions: {len(functions)}
                - Classes: {len(classes)}
                - Lines: {len(code.split('\n'))}
                - Estimated complexity: {'High' if len(functions) > 5 else 'Medium' if len(functions) > 2 else 'Low'}
                """
            except SyntaxError:
                return "Code has syntax errors"

        def check_style(code: str) -> str:
            """Check Python style guidelines"""
            issues = []

            # Check for common style issues
            if re.search(r'def\s+\w+\s*\(\s*\)', code):
                issues.append("Consider adding type hints to functions")

            if 'import *' in code:
                issues.append("Avoid wildcard imports")

            if len([line for line in code.split('\n') if len(line) > 88]) > 0:
                issues.append("Some lines exceed 88 characters")

            return "Style Issues:\n" + "\n".join(f"- {issue}" for issue in issues) if issues else "No style issues found"

        def security_check(code: str) -> str:
            """Basic security checks"""
            security_issues = []

            if 'eval(' in code:
                security_issues.append("Avoid using eval() - security risk")

            if 'exec(' in code:
                security_issues.append("Avoid using exec() - security risk")

            if re.search(r'open\([^)]*["\']w["\']', code):
                security_issues.append("File writing detected - ensure proper validation")

            return "Security Issues:\n" + "\n".join(f"- {issue}" for issue in security_issues) if security_issues else "No security issues found"

        return [
            Tool(name="complexity_analysis", description="Analyze code complexity", func=analyze_complexity),
            Tool(name="style_check", description="Check code style", func=check_style),
            Tool(name="security_check", description="Check for security issues", func=security_check)
        ]

    def _create_agent(self):
        """Create the ReAct agent"""
        prompt = PromptTemplate.from_template("""
        You are a code review expert. Analyze the given code using available tools and provide comprehensive feedback.

        Available tools:
        {tools}

        Use the following format:
        Question: the input question you must answer
        Thought: you should always think about what to do
        Action: the action to take, should be one of [{tool_names}]
        Action Input: the input to the action
        Observation: the result of the action
        ... (this Thought/Action/Action Input/Observation can repeat N times)
        Thought: I now know the final answer
        Final Answer: the final answer to the original input question

        Question: {input}
        {agent_scratchpad}
        """)

        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(agent=agent, tools=self.tools, verbose=True)

    def review_code(self, code: str) -> str:
        """Review the provided code"""
        return self.agent.invoke({
            "input": f"Please review this Python code:\n\n{code}"
        })

# Usage example
code_to_review = '''
def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result

def unsafe_function(user_input):
    return eval(user_input)  # Security issue
'''

reviewer = CodeReviewAgent()
review_result = reviewer.review_code(code_to_review)
print(review_result)

### Exercise 3: RAG Document Q&A System

```python
from langchain.document_loaders import DirectoryLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain.llms import OpenAI
from typing import List, Dict, Optional
import os

class DocumentQASystem:
    def __init__(self, documents_path: str):
        self.documents_path = documents_path
        self.embeddings = OpenAIEmbeddings()
        self.llm = OpenAI(temperature=0)
        self.vectorstore: Optional[FAISS] = None
        self.qa_chain: Optional[RetrievalQA] = None

    def load_documents(self) -> List:
        """Load documents from directory"""
        loader = DirectoryLoader(
            self.documents_path,
            glob="**/*.txt",
            loader_cls=TextLoader
        )
        documents = loader.load()

        # Split documents into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len
        )

        return text_splitter.split_documents(documents)

    def build_index(self):
        """Build vector index from documents"""
        documents = self.load_documents()

        if not documents:
            raise ValueError("No documents found to index")

        self.vectorstore = FAISS.from_documents(documents, self.embeddings)

        # Create QA chain
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=self.vectorstore.as_retriever(
                search_kwargs={"k": 3}  # Return top 3 relevant chunks
            ),
            return_source_documents=True
        )

    def query(self, question: str) -> Dict[str, any]:
        """Query the document collection"""
        if not self.qa_chain:
            raise ValueError("Index not built. Call build_index() first.")

        result = self.qa_chain({"query": question})

        return {
            "answer": result["result"],
            "sources": [doc.metadata.get("source", "Unknown") for doc in result["source_documents"]],
            "confidence": self._calculate_confidence(result["source_documents"])
        }

    def _calculate_confidence(self, source_docs: List) -> float:
        """Calculate confidence score based on source document relevance"""
        if not source_docs:
            return 0.0

        # Simple confidence calculation based on number of sources
        # In practice, you'd use more sophisticated methods
        return min(len(source_docs) / 3.0, 1.0)

    def add_document(self, content: str, metadata: Dict[str, str]):
        """Add a new document to the index"""
        from langchain.schema import Document

        doc = Document(page_content=content, metadata=metadata)

        if self.vectorstore:
            # Add to existing index
            self.vectorstore.add_documents([doc])
        else:
            # Create new index
            self.vectorstore = FAISS.from_documents([doc], self.embeddings)

# Usage example
qa_system = DocumentQASystem("./documents")

# Build the index
qa_system.build_index()

# Query the system
result = qa_system.query("How do I implement error handling in Python?")
print(f"Answer: {result['answer']}")
print(f"Sources: {result['sources']}")
print(f"Confidence: {result['confidence']:.2f}")

# Add a new document
qa_system.add_document(
    "Python error handling uses try-except blocks to catch and handle exceptions gracefully.",
    {"source": "python_guide.txt", "topic": "error_handling"}
)

## 🎯 Interview Simulation Exercises

### Simulation 1: Live Coding - LangChain Agent (45 minutes)

**Scenario**: Build an AI agent that can analyze GitHub repositories and provide insights.

**Requirements**:
1. Create a custom tool that fetches repository information
2. Implement memory to track analysis history
3. Handle API rate limiting and errors gracefully
4. Provide structured output with recommendations

**Solution Framework**:

```python
from langchain.agents import Tool, AgentExecutor, create_react_agent
from langchain.memory import ConversationBufferMemory
from langchain.prompts import PromptTemplate
import requests
import time
from typing import Dict, List

class GitHubAnalysisAgent:
    def __init__(self, github_token: str):
        self.github_token = github_token
        self.memory = ConversationBufferMemory()
        self.rate_limit_remaining = 5000
        self.tools = self._create_tools()

    def _create_tools(self) -> List[Tool]:
        def fetch_repo_info(repo_url: str) -> str:
            """Fetch repository information from GitHub API"""
            try:
                # Extract owner/repo from URL
                parts = repo_url.replace("https://github.com/", "").split("/")
                if len(parts) < 2:
                    return "Invalid repository URL format"

                owner, repo = parts[0], parts[1]

                # Check rate limit
                if self.rate_limit_remaining < 10:
                    return "Rate limit exceeded. Please try again later."

                # Fetch repository data
                headers = {"Authorization": f"token {self.github_token}"}
                response = requests.get(f"https://api.github.com/repos/{owner}/{repo}", headers=headers)

                if response.status_code == 404:
                    return "Repository not found"

                data = response.json()
                self.rate_limit_remaining = int(response.headers.get("X-RateLimit-Remaining", 0))

                return f"""
                Repository Analysis:
                - Name: {data['name']}
                - Language: {data.get('language', 'Not specified')}
                - Stars: {data['stargazers_count']}
                - Forks: {data['forks_count']}
                - Issues: {data['open_issues_count']}
                - Last Updated: {data['updated_at']}
                - Description: {data.get('description', 'No description')}
                """

            except Exception as e:
                return f"Error fetching repository data: {str(e)}"

        def analyze_languages(repo_url: str) -> str:
            """Analyze programming languages used in repository"""
            try:
                parts = repo_url.replace("https://github.com/", "").split("/")
                owner, repo = parts[0], parts[1]

                headers = {"Authorization": f"token {self.github_token}"}
                response = requests.get(f"https://api.github.com/repos/{owner}/{repo}/languages", headers=headers)

                if response.status_code != 200:
                    return "Could not fetch language data"

                languages = response.json()
                total_bytes = sum(languages.values())

                analysis = "Language Distribution:\n"
                for lang, bytes_count in sorted(languages.items(), key=lambda x: x[1], reverse=True):
                    percentage = (bytes_count / total_bytes) * 100
                    analysis += f"- {lang}: {percentage:.1f}%\n"

                return analysis

            except Exception as e:
                return f"Error analyzing languages: {str(e)}"

        return [
            Tool(name="fetch_repo_info", description="Fetch basic repository information", func=fetch_repo_info),
            Tool(name="analyze_languages", description="Analyze programming languages in repository", func=analyze_languages)
        ]

    def analyze_repository(self, repo_url: str) -> str:
        """Analyze a GitHub repository and provide insights"""
        prompt = f"""
        Analyze the GitHub repository at {repo_url} and provide comprehensive insights.
        Use the available tools to gather information and provide recommendations for:
        1. Code quality assessment
        2. Community engagement
        3. Maintenance status
        4. Technology stack evaluation
        """

        # This would use the actual agent execution
        # For simulation, we'll provide a structured response
        return "Repository analysis completed with recommendations."

# Interview Question: "How would you handle rate limiting in this implementation?"
# Expected Discussion: Exponential backoff, caching, batch requests, etc.
```

### Simulation 2: System Design - Scalable RAG Pipeline

**Scenario**: Design a document Q&A system that can handle 10,000+ documents and 1000+ concurrent users.

**Key Discussion Points**:

1. **Architecture Components**:
   - Document ingestion pipeline
   - Vector database selection (Pinecone vs Weaviate vs FAISS)
   - Load balancing and caching strategies
   - Monitoring and observability

2. **Scalability Considerations**:
   - Horizontal scaling of embedding generation
   - Vector index sharding and replication
   - Query optimization and caching
   - Cost optimization strategies

3. **Implementation Sketch**:

```python
from dataclasses import dataclass
from typing import List, Dict, Optional
import asyncio
from abc import ABC, abstractmethod

@dataclass
class Document:
    id: str
    content: str
    metadata: Dict[str, any]
    embedding: Optional[List[float]] = None

class VectorStore(ABC):
    @abstractmethod
    async def add_documents(self, documents: List[Document]) -> None:
        pass

    @abstractmethod
    async def search(self, query_embedding: List[float], k: int = 5) -> List[Document]:
        pass

class ScalableRAGPipeline:
    def __init__(self, vector_store: VectorStore, embedding_service, llm_service):
        self.vector_store = vector_store
        self.embedding_service = embedding_service
        self.llm_service = llm_service
        self.cache = {}  # Redis in production

    async def ingest_documents(self, documents: List[Document]) -> None:
        """Ingest documents with parallel embedding generation"""
        # Generate embeddings in batches
        batch_size = 100
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]

            # Parallel embedding generation
            embedding_tasks = [
                self.embedding_service.generate_embedding(doc.content)
                for doc in batch
            ]
            embeddings = await asyncio.gather(*embedding_tasks)

            # Assign embeddings to documents
            for doc, embedding in zip(batch, embeddings):
                doc.embedding = embedding

            # Store in vector database
            await self.vector_store.add_documents(batch)

    async def query(self, question: str) -> Dict[str, any]:
        """Query with caching and optimization"""
        # Check cache first
        cache_key = f"query:{hash(question)}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Generate query embedding
        query_embedding = await self.embedding_service.generate_embedding(question)

        # Retrieve relevant documents
        relevant_docs = await self.vector_store.search(query_embedding, k=5)

        # Generate answer using LLM
        context = "\n".join([doc.content for doc in relevant_docs])
        answer = await self.llm_service.generate_answer(question, context)

        result = {
            "answer": answer,
            "sources": [doc.metadata for doc in relevant_docs],
            "confidence": self._calculate_confidence(relevant_docs)
        }

        # Cache result
        self.cache[cache_key] = result
        return result

    def _calculate_confidence(self, docs: List[Document]) -> float:
        # Implement confidence calculation
        return 0.85

# Interview Discussion: How would you handle cache invalidation,
# vector index updates, and ensuring consistency?
```

### Simulation 3: Debugging LLM Integration Issues

**Scenario**: An AI-powered code review system is producing inconsistent results and occasionally failing.

**Common Issues to Identify and Fix**:

```python
# Issue 1: Improper error handling
async def broken_llm_call(prompt: str) -> str:
    # ❌ No error handling
    response = await openai.ChatCompletion.acreate(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content

# ✅ Fixed version
async def robust_llm_call(prompt: str) -> str:
    try:
        response = await openai.ChatCompletion.acreate(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            timeout=30
        )
        return response.choices[0].message.content
    except openai.error.RateLimitError:
        await asyncio.sleep(60)  # Wait and retry
        return await robust_llm_call(prompt)
    except openai.error.APIError as e:
        logging.error(f"OpenAI API error: {e}")
        return "Error: Unable to process request"
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return "Error: Service temporarily unavailable"

# Issue 2: Token limit exceeded
def broken_prompt_builder(code: str, context: str) -> str:
    # ❌ No token limit checking
    return f"""
    Review this code:
    {code}

    Context:
    {context}

    Provide detailed feedback...
    """

# ✅ Fixed version with token management
import tiktoken

def safe_prompt_builder(code: str, context: str, max_tokens: int = 3000) -> str:
    encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")

    base_prompt = "Review this code and provide feedback:\n"

    # Calculate available tokens for code and context
    base_tokens = len(encoding.encode(base_prompt))
    available_tokens = max_tokens - base_tokens - 500  # Reserve for response

    # Truncate code and context if necessary
    code_tokens = len(encoding.encode(code))
    context_tokens = len(encoding.encode(context))

    if code_tokens + context_tokens > available_tokens:
        # Prioritize code over context
        if code_tokens > available_tokens * 0.7:
            code = encoding.decode(encoding.encode(code)[:int(available_tokens * 0.7)])

        remaining_tokens = available_tokens - len(encoding.encode(code))
        if context_tokens > remaining_tokens:
            context = encoding.decode(encoding.encode(context)[:remaining_tokens])

    return f"{base_prompt}\nCode:\n{code}\n\nContext:\n{context}"

# Interview Question: "What other issues might cause inconsistent LLM results?"
# Expected answers: Temperature settings, prompt variations, model version changes, etc.

## 🚀 Performance Optimization Techniques

### Batch Processing for LLM Operations

```python
import asyncio
from typing import List, Dict, Any
import time

class BatchLLMProcessor:
    def __init__(self, batch_size: int = 10, max_wait_time: float = 2.0):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_requests: List[Dict[str, Any]] = []
        self.processing = False

    async def process_request(self, prompt: str, request_id: str) -> str:
        """Add request to batch and wait for result"""
        future = asyncio.Future()

        self.pending_requests.append({
            "prompt": prompt,
            "request_id": request_id,
            "future": future
        })

        # Start batch processing if not already running
        if not self.processing:
            asyncio.create_task(self._process_batch())

        return await future

    async def _process_batch(self):
        """Process accumulated requests in batches"""
        self.processing = True

        while self.pending_requests:
            # Wait for batch to fill or timeout
            start_time = time.time()
            while (len(self.pending_requests) < self.batch_size and
                   time.time() - start_time < self.max_wait_time):
                await asyncio.sleep(0.1)

            # Process current batch
            batch = self.pending_requests[:self.batch_size]
            self.pending_requests = self.pending_requests[self.batch_size:]

            # Simulate batch LLM processing
            results = await self._call_llm_batch([req["prompt"] for req in batch])

            # Return results to waiting futures
            for request, result in zip(batch, results):
                request["future"].set_result(result)

        self.processing = False

    async def _call_llm_batch(self, prompts: List[str]) -> List[str]:
        """Simulate batch LLM API call"""
        # In practice, this would use actual batch API
        await asyncio.sleep(1)  # Simulate API call
        return [f"Response to: {prompt[:50]}..." for prompt in prompts]

# Usage
processor = BatchLLMProcessor(batch_size=5, max_wait_time=1.0)

async def example_usage():
    tasks = [
        processor.process_request(f"Analyze code snippet {i}", f"req_{i}")
        for i in range(12)
    ]

    results = await asyncio.gather(*tasks)
    for i, result in enumerate(results):
        print(f"Request {i}: {result}")

asyncio.run(example_usage())
```

### Caching Strategies for AI Applications

```python
import hashlib
import json
import time
from typing import Any, Optional, Dict
from functools import wraps

class AICache:
    def __init__(self, ttl: int = 3600):  # 1 hour TTL
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.ttl = ttl

    def _generate_key(self, prompt: str, model: str, temperature: float) -> str:
        """Generate cache key from parameters"""
        data = {"prompt": prompt, "model": model, "temperature": temperature}
        return hashlib.md5(json.dumps(data, sort_keys=True).encode()).hexdigest()

    def get(self, prompt: str, model: str = "gpt-3.5-turbo", temperature: float = 0.7) -> Optional[str]:
        """Get cached response if available and not expired"""
        key = self._generate_key(prompt, model, temperature)

        if key in self.cache:
            entry = self.cache[key]
            if time.time() - entry["timestamp"] < self.ttl:
                return entry["response"]
            else:
                # Remove expired entry
                del self.cache[key]

        return None

    def set(self, prompt: str, response: str, model: str = "gpt-3.5-turbo", temperature: float = 0.7):
        """Cache response"""
        key = self._generate_key(prompt, model, temperature)
        self.cache[key] = {
            "response": response,
            "timestamp": time.time()
        }

    def clear_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time - entry["timestamp"] >= self.ttl
        ]

        for key in expired_keys:
            del self.cache[key]

def cached_llm_call(cache: AICache):
    """Decorator for caching LLM calls"""
    def decorator(func):
        @wraps(func)
        async def wrapper(prompt: str, model: str = "gpt-3.5-turbo", temperature: float = 0.7, **kwargs):
            # Check cache first
            cached_response = cache.get(prompt, model, temperature)
            if cached_response:
                return cached_response

            # Call function and cache result
            response = await func(prompt, model=model, temperature=temperature, **kwargs)
            cache.set(prompt, response, model, temperature)

            return response
        return wrapper
    return decorator

# Usage
cache = AICache(ttl=1800)  # 30 minutes

@cached_llm_call(cache)
async def call_llm(prompt: str, model: str = "gpt-3.5-turbo", temperature: float = 0.7) -> str:
    # Simulate LLM call
    await asyncio.sleep(1)
    return f"LLM response to: {prompt[:30]}..."
```

## 📝 Interview Tips and Best Practices

### Key Points to Emphasize

1. **Python vs TypeScript Transition**:
   - Highlight understanding of dynamic vs static typing
   - Show familiarity with Python's async/await patterns
   - Demonstrate knowledge of Python-specific features (list comprehensions, decorators)

2. **LangChain Expertise**:
   - Explain component architecture (chains, agents, memory, tools)
   - Show understanding of RAG implementation patterns
   - Demonstrate ability to create custom components

3. **Production Considerations**:
   - Error handling and retry logic
   - Cost optimization and token management
   - Monitoring and observability
   - Scalability patterns

### Common Interview Questions and Answers

**Q: "How would you migrate a TypeScript AI application to Python?"**

A: "I'd approach this systematically:
1. **Architecture Analysis**: Map TypeScript classes to Python equivalents using dataclasses or Pydantic models
2. **Async Patterns**: Convert Promise-based code to asyncio patterns
3. **Type Safety**: Implement type hints and use mypy for static analysis
4. **Testing**: Port unit tests and add Python-specific test patterns
5. **Dependencies**: Replace npm packages with Python equivalents (axios → aiohttp, etc.)
6. **Gradual Migration**: Use API boundaries to migrate components incrementally"

**Q: "Explain the difference between LangChain chains and agents."**

A: "Chains are deterministic sequences of operations - you define the exact flow from input to output. They're great for predictable workflows like document summarization or data transformation.

Agents are autonomous systems that can make decisions about which tools to use and when. They use reasoning patterns like ReAct (Reasoning + Acting) to break down complex tasks and choose appropriate actions dynamically. Agents are better for open-ended tasks where the solution path isn't predetermined."

**Q: "How do you handle LLM hallucinations in production?"**

A: "Multiple strategies:
1. **Prompt Engineering**: Use specific instructions and examples to guide behavior
2. **Output Validation**: Implement schema validation and fact-checking
3. **Confidence Scoring**: Assess response reliability using multiple metrics
4. **Retrieval Augmentation**: Ground responses in verified source material
5. **Human-in-the-Loop**: Flag uncertain responses for human review
6. **A/B Testing**: Compare different prompt strategies and model configurations"

### Code Review Checklist for AI Applications

- [ ] **Error Handling**: Proper try/catch for API calls and model operations
- [ ] **Resource Management**: Efficient token usage and memory management
- [ ] **Type Safety**: Comprehensive type hints and validation
- [ ] **Async Patterns**: Proper use of asyncio for concurrent operations
- [ ] **Caching**: Appropriate caching strategies for expensive operations
- [ ] **Monitoring**: Logging and metrics for debugging and optimization
- [ ] **Security**: Input validation and API key protection
- [ ] **Testing**: Unit tests for AI components and integration tests

## 🎯 Final Preparation Checklist

### Technical Skills Verification
- [ ] Can write idiomatic Python code without TypeScript syntax errors
- [ ] Understands LangChain component architecture and can build custom chains
- [ ] Can implement RAG systems with proper error handling
- [ ] Demonstrates async programming patterns in Python
- [ ] Shows understanding of LLM cost optimization strategies
- [ ] Can debug and optimize AI workflows effectively

### Interview Day Strategy
- [ ] Practice explaining Python concepts with TypeScript comparisons
- [ ] Prepare examples of LangChain implementations you've built
- [ ] Review common AI/ML production challenges and solutions
- [ ] Practice live coding with Python in a shared environment
- [ ] Prepare thoughtful questions about Augment Code's AI architecture

### Key Success Factors
- [ ] Demonstrate practical experience with LLM applications
- [ ] Show understanding of production AI system challenges
- [ ] Explain trade-offs clearly in technical decisions
- [ ] Ask clarifying questions about requirements
- [ ] Think aloud during coding exercises to show reasoning process

Remember: The goal is to show you can effectively transition your TypeScript expertise to Python while building robust, production-ready AI applications using LangChain and modern LLM patterns.
```
```

This is the foundation of the coding preparation guide. The file is approaching the 300-line limit, so I'll continue adding more content using the str-replace-editor tool.
