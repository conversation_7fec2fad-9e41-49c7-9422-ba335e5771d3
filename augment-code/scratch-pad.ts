/* ------------------------- implement Promise class ------------------------ */

class MyPromise<T> {
  private state: 'pending' | 'rejected' | 'fulfilled' = 'pending';
  private value: T | undefined;
  private handlers: Array<{
    onFulfilled: (v: T) => void;
    onRejected: (reason: any) => void;
  }> = [];

  constructor(executor: (
    resolve: (v: T) => void,
    reject: (reason: any) => void
  ) => void) {
    const resolve = (v: T) => {
      this.state = 'fulfilled';
      this.value = v;
      // when is handler pushed?
      this.handlers.forEach(handler => handler.onFulfilled(v));
    }

    const reject = (reason: any) => {
      this.state = 'rejected';
      this.value = reason;
      // when is handler pushed?
      this.handlers.forEach(handler => handler.onRejected(reason));
    }

    executor(resolve, reject);
  }

  then<U>(onFulfilled?: (v: T) => void, onRejected?: (v: unknown) => void): MyPromise<U> {
    return new MyPromise((resolve, reject) => {
      const handlers = {
        onFulfilled: onFulfilled ?? (value => resolve(value as unknown as U)),
        onRejected: onRejected ?? (value => reject(value))
      };

      if (this.state === 'pending') {
        this.handlers.push(handlers);
      } else if (this.state === 'fulfilled') {
        handlers.onFulfilled(this.value as T);
      } else {
        handlers.onRejected(this.value as T);
      }
    });
  }

  catch(onRejected: (reject: any) => void) {
    return this.then(undefined, onRejected)
  }
}

// e.g. usage
function foo(callback: () => void) {
  const sleepPromise = new Promise((resolve: (v: unknown) => void, reject: (v: unknown) => void) => {
    try {
      setTimeout(() => {
        resolve(true);
      }, 1000);
    } catch {
      reject(new Error());
    }
  });

  sleepPromise.then(resolvedVal => {
    callback(resolvedVal);
  }, rejectedVal => {
    throw rejectedVal;
  })
}

/* ------------------------------- Web Crawler ------------------------------ */
type PageMeta = { url: string; depth: number };

async function crawlBfs(_pageMeta: PageMeta | string, options = { maxDepth: 3, visited: new Set<string>() }, _allUrls = new Map<string, PageMeta>()) {
  let pageMeta: PageMeta = _pageMeta as unknown as PageMeta;

  if (typeof _pageMeta === 'string') {
    pageMeta = {
      url: _pageMeta,
      depth: 0
    }
  }

  if (pageMeta.depth >= options.maxDepth) {
    return _allUrls;
  }

  // check circular links, i.e. visited links
  // if visited, skip and return
  if (options.visited.has(pageMeta.url)) {
    return _allUrls;
  }

  // push all urls to the queue
  const currentPageAllUrls = getAllUrls(pageMeta);
  options.visited.add(pageMeta.url);

  currentPageAllUrls.forEach(url => {
    _allUrls.set(url, ({ url, depth: pageMeta.depth + 1 });
  });

  // get the next url in the allUrls
  const nextUrl = _allUrls.next();

  if (!nextUrl) {
    return _allUrls;
  }

  // rate limting control with debounce
  await new Promise(resolve => setTimeout(resolve, 300));
  return crawlBfs(nextUrl, options, _allUrls);
}

const allUrls = crawlBfs('http://example.com');